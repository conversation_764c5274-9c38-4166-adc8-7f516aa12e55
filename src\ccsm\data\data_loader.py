"""
数据加载模块
负责加载外部数据文件，包括基准值、寿命表和人口构成数据
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings

class DataLoader:
    """数据加载器类"""

    def __init__(self, data_dir: Optional[str] = None):
        """
        初始化数据加载器

        Args:
            data_dir: 数据文件目录，默认为项目根目录
        """
        if data_dir is None:
            # 默认使用项目根目录
            self.data_dir = Path(__file__).parent.parent.parent.parent
        else:
            self.data_dir = Path(data_dir)

        # 数据文件路径
        self.benchmark_file = self.data_dir / "default_benchmarks.xlsx"
        self.lifetable_file = self.data_dir / "lifetable.xlsx"
        self.population_file = self.data_dir / "population.xlsx"
        self.screening_file = self.data_dir / "screening.xlsx"

        # 缓存加载的数据
        self._benchmark_data = None
        self._lifetable_data = None
        self._population_data = None
        self._screening_data = None

    def load_benchmark_data(self) -> Dict[str, Dict]:
        """
        加载校准基准值数据

        Returns:
            包含各种基准指标的字典
        """
        if self._benchmark_data is not None:
            return self._benchmark_data

        try:
            # 读取Excel文件的所有工作表
            excel_file = pd.ExcelFile(self.benchmark_file)
            benchmark_data = {}

            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(self.benchmark_file, sheet_name=sheet_name)
                benchmark_data[sheet_name] = df

            # 处理常见的基准指标
            processed_data = self._process_benchmark_data(benchmark_data)

            self._benchmark_data = processed_data
            print(f"✅ 成功加载基准数据，包含 {len(processed_data)} 个指标组")

            return processed_data

        except FileNotFoundError:
            print(f"❌ 未找到基准数据文件: {self.benchmark_file}")
            return self._get_default_benchmark_data()
        except Exception as e:
            print(f"❌ 加载基准数据失败: {e}")
            return self._get_default_benchmark_data()

    def load_lifetable_data(self) -> Dict[str, Dict[int, float]]:
        """
        加载寿命表数据

        Returns:
            按性别和年龄分组的死亡率数据
        """
        if self._lifetable_data is not None:
            return self._lifetable_data

        try:
            # 读取寿命表数据
            df = pd.read_excel(self.lifetable_file)

            # 处理寿命表数据
            lifetable_data = self._process_lifetable_data(df)

            self._lifetable_data = lifetable_data
            print(f"✅ 成功加载寿命表数据")

            return lifetable_data

        except FileNotFoundError:
            print(f"❌ 未找到寿命表文件: {self.lifetable_file}")
            return self._get_default_lifetable_data()
        except Exception as e:
            print(f"❌ 加载寿命表数据失败: {e}")
            return self._get_default_lifetable_data()

    def load_population_data(self) -> Dict[str, any]:
        """
        加载人口构成数据

        Returns:
            包含年龄分布和性别比例的数据
        """
        if self._population_data is not None:
            return self._population_data

        try:
            # 读取人口构成数据
            df = pd.read_excel(self.population_file)

            # 处理人口数据
            population_data = self._process_population_data(df)

            self._population_data = population_data
            print(f"✅ 成功加载南山区人口构成数据")

            return population_data

        except FileNotFoundError:
            print(f"❌ 未找到人口构成文件: {self.population_file}")
            return self._get_default_population_data()
        except Exception as e:
            print(f"❌ 加载人口构成数据失败: {e}")
            return self._get_default_population_data()
    
    def load_screening_data(self) -> Dict[str, Dict]:
        """
        加载筛查数据

        Returns:
            包含筛查性能的数据
        """
        if self._screening_data is not None:
            return self._screening_data

        try:
            # 读取筛查数据
            df = pd.read_excel(self.screening_file)

            # 处理筛查数据
            screening_data = self._extract_screening_data(df)

            self._screening_data = screening_data
            print(f"✅ 成功加载筛查数据")

            return screening_data

        except FileNotFoundError:
            print(f"❌ 未找到筛查数据文件: {self.screening_file}")
            return {}
        except Exception as e:
            print(f"❌ 加载筛查数据失败: {e}")
            return {}

    def load_custom_lifetable(self, file_path: str) -> Dict[str, Dict[int, float]]:
        """
        从自定义文件加载生命表数据

        Args:
            file_path: 生命表文件路径，支持CSV和Excel格式

        Returns:
            按性别和年龄分组的死亡率数据
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                raise FileNotFoundError(f"生命表文件不存在: {file_path}")

            # 根据文件扩展名选择读取方式
            if file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            elif file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}，仅支持 .xlsx, .xls, .csv")

            # 验证数据格式
            if df.empty:
                raise ValueError("生命表文件为空")

            # 处理生命表数据
            lifetable_data = self._process_lifetable_data(df)

            # 验证处理结果
            if not any(lifetable_data.values()):
                raise ValueError("未能从文件中提取有效的生命表数据，请检查文件格式")

            print(f"✅ 成功加载自定义生命表: {file_path}")
            print(f"   - 男性数据: {len(lifetable_data.get('male', {}))} 个年龄组")
            print(f"   - 女性数据: {len(lifetable_data.get('female', {}))} 个年龄组")
            print(f"   - 总体数据: {len(lifetable_data.get('total', {}))} 个年龄组")

            return lifetable_data

        except Exception as e:
            print(f"❌ 加载自定义生命表失败: {e}")
            print("💡 请确保文件格式正确，包含年龄列和死亡率列")
            print("💡 支持的列名示例:")
            print("   - 年龄列: 'age', '年龄', 'Age', 'x'")
            print("   - 男性死亡率: 'male', '男', 'qx_m', 'mx_m'")
            print("   - 女性死亡率: 'female', '女', 'qx_f', 'mx_f'")
            print("   - 总体死亡率: 'total', 'qx', 'mx', '死亡率'")
            return self._get_default_lifetable_data()


    def _process_benchmark_data(self, raw_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """处理基准数据"""
        processed = {}

        for sheet_name, df in raw_data.items():
            try:
                if 'incidence' in sheet_name.lower() or '发病' in sheet_name:
                    # 处理发病率数据
                    processed['incidence_rates'] = self._extract_age_gender_rates(df)

                elif 'mortality' in sheet_name.lower() or '死亡' in sheet_name:
                    # 处理死亡率数据
                    processed['mortality_rates'] = self._extract_age_gender_rates(df)

                elif 'low_risk_adenoma' in sheet_name.lower() or '腺瘤' in sheet_name:
                    # 处理低风险腺瘤患病率数据
                    processed['low_risk_adenoma_prevalence'] = self._extract_age_gender_rates(df)

                elif 'high_risk_adenoma' in sheet_name.lower() or '腺瘤' in sheet_name:
                    # 处理高风险腺瘤患病率数据
                    processed['high_risk_adenoma_prevalence'] = self._extract_age_gender_rates(df)

                else:
                    # 其他数据直接保存
                    processed[sheet_name] = df.to_dict('records')

            except Exception as e:
                print(f"⚠️ 处理工作表 {sheet_name} 时出错: {e}")
                continue

        return processed

    def _extract_age_gender_rates(self, df: pd.DataFrame) -> Dict[str, Dict[int, float]]:
        """从DataFrame中提取按年龄和性别分组的比率数据"""
        rates = {'male': {}, 'female': {}, 'total': {}}

        # 检查数据格式：基准数据是按行存储的，格式为 [metric, gender, age, value]
        if 'gender' in df.columns and 'age' in df.columns and 'value' in df.columns:
            # 按行处理数据
            for _, row in df.iterrows():
                try:
                    gender = str(row['gender']).lower()
                    age = int(row['age'])
                    value = float(row['value'])

                    # 转换为每10万人的比率（如果值大于1，假设是每10万人）
                    if value > 1:
                        value = value / 100000.0

                    if gender in ['male', '男', '男性']:
                        rates['male'][age] = value
                    elif gender in ['female', '女', '女性']:
                        rates['female'][age] = value
                    elif gender in ['total', '合计', '总计']:
                        rates['total'][age] = value

                except (ValueError, TypeError, KeyError):
                    continue
        else:
            # 原有的按列处理逻辑（作为备用）
            # 尝试识别年龄列
            age_col = None
            for col in df.columns:
                if any(keyword in str(col).lower() for keyword in ['age', '年龄', 'Age']):
                    age_col = col
                    break

            if age_col is None and len(df.columns) > 0:
                age_col = df.columns[0]  # 使用第一列作为年龄列

            # 尝试识别性别相关列
            for col in df.columns:
                col_name = str(col).lower()
                if any(keyword in col_name for keyword in ['male', '男', '男性']):
                    for _, row in df.iterrows():
                        try:
                            age = int(row[age_col])
                            rate = float(row[col])
                            rates['male'][age] = rate
                        except (ValueError, TypeError):
                            continue

                elif any(keyword in col_name for keyword in ['female', '女', '女性']):
                    for _, row in df.iterrows():
                        try:
                            age = int(row[age_col])
                            rate = float(row[col])
                            rates['female'][age] = rate
                        except (ValueError, TypeError):
                            continue

                elif any(keyword in col_name for keyword in ['total', '合计', '总计', 'rate', '率']):
                    for _, row in df.iterrows():
                        try:
                            age = int(row[age_col])
                            rate = float(row[col])
                            rates['total'][age] = rate
                        except (ValueError, TypeError):
                            continue

        return rates

    def _extract_screening_data(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取筛查性能数据"""
        screening_data = {}

        for _, row in df.iterrows():
            try:
                # 尝试提取筛查工具和性能指标
                tool_name = str(row.iloc[0]).lower()

                if 'fit' in tool_name:
                    screening_data['fit_sensitivity'] = float(row.iloc[1]) if len(row) > 1 else 0.8
                    screening_data['fit_specificity'] = float(row.iloc[2]) if len(row) > 2 else 0.95

                elif 'colonoscopy' in tool_name or '结肠镜' in tool_name:
                    screening_data['colonoscopy_sensitivity'] = float(row.iloc[1]) if len(row) > 1 else 0.95
                    screening_data['colonoscopy_specificity'] = float(row.iloc[2]) if len(row) > 2 else 0.99

            except (ValueError, TypeError):
                continue

        return screening_data

    def _process_lifetable_data(self, df: pd.DataFrame) -> Dict[str, Dict[int, float]]:
        """处理寿命表数据"""
        lifetable = {'male': {}, 'female': {}, 'total': {}}

        # 尝试识别列名
        age_col = None
        for col in df.columns:
            if any(keyword in str(col).lower() for keyword in ['age', '年龄', 'x']):
                age_col = col
                break

        if age_col is None:
            age_col = df.columns[0]

        # 提取死亡率数据
        for col in df.columns:
            col_name = str(col).lower()

            if any(keyword in col_name for keyword in ['male', '男', 'qx_m', 'mx_m']):
                for _, row in df.iterrows():
                    try:
                        age = int(row[age_col])
                        mortality_rate = float(row[col])
                        lifetable['male'][age] = mortality_rate
                    except (ValueError, TypeError):
                        continue

            elif any(keyword in col_name for keyword in ['female', '女', 'qx_f', 'mx_f']):
                for _, row in df.iterrows():
                    try:
                        age = int(row[age_col])
                        mortality_rate = float(row[col])
                        lifetable['female'][age] = mortality_rate
                    except (ValueError, TypeError):
                        continue

            elif any(keyword in col_name for keyword in ['total', 'qx', 'mx', '死亡率']):
                for _, row in df.iterrows():
                    try:
                        age = int(row[age_col])
                        mortality_rate = float(row[col])
                        lifetable['total'][age] = mortality_rate
                    except (ValueError, TypeError):
                        continue

        return lifetable

    def _process_population_data(self, df: pd.DataFrame) -> Dict[str, any]:
        """处理人口构成数据"""
        population_data = {
            'age_distribution': {},
            'gender_ratio': 0.5,
            'total_population': 0
        }

        # 提取人口数据
        total_pop = 0
        male_pop = 0
        female_pop = 0

        # 处理每一行数据
        for _, row in df.iterrows():
            try:
                # 解析年龄组
                age_group = str(row['age_groups'])
                male_count = float(row['male'])
                female_count = float(row['female'])
                total_count = float(row['total'])

                # 累计性别人口
                male_pop += male_count
                female_pop += female_count
                total_pop += total_count

                # 处理年龄组，转换为中位年龄
                if '-' in age_group:
                    # 处理年龄段，如"65-69"
                    age_parts = age_group.split('-')
                    if len(age_parts) == 2:
                        try:
                            start_age = int(age_parts[0])
                            end_age = int(age_parts[1])
                            mid_age = (start_age + end_age) // 2
                        except ValueError:
                            continue
                    else:
                        continue
                elif '+' in age_group:
                    # 处理开放年龄组，如"85+"
                    try:
                        mid_age = int(age_group.replace('+', ''))
                    except ValueError:
                        continue
                else:
                    # 处理单一年龄
                    try:
                        mid_age = int(age_group)
                    except ValueError:
                        continue

                # 存储年龄分布（使用总人口）
                population_data['age_distribution'][mid_age] = total_count

            except (ValueError, TypeError, KeyError) as e:
                print(f"⚠️ 跳过无效行: {e}")
                continue

        # 计算性别比例（男性比例）
        if male_pop + female_pop > 0:
            population_data['gender_ratio'] = male_pop / (male_pop + female_pop)
            print(f"📊 计算得出性别比例 - 男性: {male_pop:,}, 女性: {female_pop:,}")
            print(f"📊 男性比例: {population_data['gender_ratio']:.3f}")

        # 标准化年龄分布为比例
        if total_pop > 0:
            for age in population_data['age_distribution']:
                population_data['age_distribution'][age] /= total_pop

            # 验证年龄分布总和
            age_sum = sum(population_data['age_distribution'].values())
            print(f"📊 年龄分布比例总和: {age_sum:.6f}")

            # 如果总和不为1，进行标准化
            if abs(age_sum - 1.0) > 0.001:
                print(f"⚠️ 年龄分布总和不为1，进行标准化")
                for age in population_data['age_distribution']:
                    population_data['age_distribution'][age] /= age_sum

        population_data['total_population'] = total_pop
        print(f"📊 总人口: {total_pop:,}")

        return population_data

    def _get_default_benchmark_data(self) -> Dict[str, Dict]:
        """获取默认基准数据"""
        return {
            'incidence_rates': {
                'male': {50: 0.0001, 55: 0.0002, 60: 0.0004, 65: 0.0008, 70: 0.0015},
                'female': {50: 0.00008, 55: 0.00015, 60: 0.0003, 65: 0.0006, 70: 0.0012},
                'total': {50: 0.00009, 55: 0.000175, 60: 0.00035, 65: 0.0007, 70: 0.00135}
            },
            'mortality_rates': {
                'male': {50: 0.00005, 55: 0.0001, 60: 0.0002, 65: 0.0004, 70: 0.0008},
                'female': {50: 0.00004, 55: 0.00008, 60: 0.00015, 65: 0.0003, 70: 0.0006},
                'total': {50: 0.000045, 55: 0.00009, 60: 0.000175, 65: 0.00035, 70: 0.0007}
            },
            'adenoma_prevalence': {
                'male': {50: 0.15, 55: 0.20, 60: 0.25, 65: 0.30, 70: 0.35},
                'female': {50: 0.10, 55: 0.15, 60: 0.20, 65: 0.25, 70: 0.30},
                'total': {50: 0.125, 55: 0.175, 60: 0.225, 65: 0.275, 70: 0.325}
            }
        }

    def _get_default_lifetable_data(self) -> Dict[str, Dict[int, float]]:
        """获取默认寿命表数据"""
        # 基于中国人口寿命表的简化数据
        ages = list(range(0, 101))
        male_rates = {}
        female_rates = {}

        for age in ages:
            if age < 1:
                male_rates[age] = 0.007
                female_rates[age] = 0.006
            elif age < 5:
                male_rates[age] = 0.0005
                female_rates[age] = 0.0004
            elif age < 15:
                male_rates[age] = 0.0002
                female_rates[age] = 0.0001
            elif age < 25:
                male_rates[age] = 0.0008
                female_rates[age] = 0.0003
            elif age < 35:
                male_rates[age] = 0.001
                female_rates[age] = 0.0005
            elif age < 45:
                male_rates[age] = 0.002
                female_rates[age] = 0.001
            elif age < 55:
                male_rates[age] = 0.005
                female_rates[age] = 0.003
            elif age < 65:
                male_rates[age] = 0.01
                female_rates[age] = 0.007
            elif age < 75:
                male_rates[age] = 0.025
                female_rates[age] = 0.018
            elif age < 85:
                male_rates[age] = 0.06
                female_rates[age] = 0.045
            else:
                male_rates[age] = 0.15
                female_rates[age] = 0.12

        return {
            'male': male_rates,
            'female': female_rates,
            'total': {age: (male_rates[age] + female_rates[age]) / 2 for age in ages}
        }

    def _get_default_population_data(self) -> Dict[str, any]:
        """获取默认人口构成数据"""
        return {
            'age_distribution': {
                40: 0.05, 45: 0.08, 50: 0.12, 55: 0.15, 60: 0.18,
                65: 0.20, 70: 0.15, 75: 0.07
            },
            'gender_ratio': 0.52,
            'total_population': 1000000
        }
